import React, { useState, useEffect, useMemo } from 'react';
import { Select, SelectProps } from 'antd';
import { nsApiManagerGroupTree } from '@/remote';
import { useDebounceFn } from 'ahooks';
import css from './DomainSelect.less';

/**
 * 领域选择器组件属性接口
 */
export interface DomainSelectProps extends Omit<SelectProps, 'options'> {
    /**
     * 选中的领域ID
     */
    value?: number | null;
    /**
     * 领域变化回调
     */
    onChange?: (value: number | null) => void;
    /**
     * 是否显示加载状态
     */
    loading?: boolean;
}

/**
 * 领域选择器组件
 * 用于选择领域，支持搜索过滤
 */
export const DomainSelect: React.FC<DomainSelectProps> = (props) => {
    // 解构props，提取需要的属性
    const { value, onChange, loading: propLoading, ...restProps } = props;

    // 组件内部状态
    const [domainValue, setDomainValue] = useState<number>(value || 0);
    const [loading, setLoading] = useState<boolean>(propLoading || false);
    const [domainSearchValue, setDomainSearchValue] = useState<string>('');
    const [domainList, setDomainList] = useState<nsApiManagerGroupTree.List[]>([]);

    // 防抖搜索函数
    const debouncedSearch = useDebounceFn(
        (v: string) => {
            setDomainSearchValue(v);
        },
        { wait: 300 }
    );

    // 过滤领域列表
    const filteredDomainList = useMemo(() => {
        if (!domainSearchValue) return domainList;
        return domainList.filter(item =>
            item.groupName.toLowerCase().includes(domainSearchValue.toLowerCase())
        );
    }, [domainList, domainSearchValue]);

    // 获取领域列表
    useEffect(() => {
        setLoading(true);
        nsApiManagerGroupTree.remote({})
            .then(res => {
                setDomainList(res.list);
                onChange?.(res.list[0].groupId);
                setLoading(false);
                setDomainValue(res.list[0].groupId);
            })
            .catch(() => {
                setLoading(false);
            });
    }, []);

    // 处理选择变化
    const handleChange = (v: number | null) => {
        onChange?.(v);
    };

    // 处理下拉框打开状态变化
    const handleDropdownVisibleChange = (open: boolean) => {
        // 当下拉框打开时，重置搜索值，显示所有数据
        if (open) {
            setDomainSearchValue('');
        }
    };

    return (
        <Select
            placeholder="请选择领域"
            showSearch
            filterOption={false}
            onSearch={(searchValue) => debouncedSearch.run(searchValue)}
            options={filteredDomainList.map(item => ({
                label: item.groupName,
                value: item.groupId
            }))}
            value={domainValue}
            onChange={handleChange}
            loading={loading || propLoading}
            className={css.domainSelect}
            onDropdownVisibleChange={handleDropdownVisibleChange}
            {...restProps}
        />
    );
};

export default DomainSelect;
