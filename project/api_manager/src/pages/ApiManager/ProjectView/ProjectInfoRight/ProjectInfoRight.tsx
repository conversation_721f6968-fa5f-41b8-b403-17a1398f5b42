import React, { useContext, useState, useEffect, useRef } from 'react';
import css from './ProjectInfoRight.less';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_api, common_system_import } from '@kid/enterprise-icon/icon/output/icons';
import { ApiManageProjectInfoContext, ApiManageProjectInfoContextType, findCatalogById, formatCatalogList } from '../ProjectInfo/ProjectInfo';
import { GroupDetail } from '@/pages/ApiManager/GroupDetail';
import { ViewApi } from '@/pages/httpApi/viewApi/ViewApi';
import { nsApiManagerSearch, nsMockManageApiManageMainCollectListGET, nsMockManageApiManageMainApiProgramCatalog, nsMockManageApiManageMainApiFollowPOST, nsMockManageApiManageMainApiProgramCatalogSubList } from '@/remote';
import { Modal, message } from 'antd';
import { ERouter } from '@/CONFIG';
import { router } from '@libs/mvvm';
import { ImportKapi, ImportKapiRef } from '../ImportKapiInterface/ImportKapiInterface';
import { ImportRepo, ImportRepoRef } from '../ImportRepo/ImportRepo';
// 使用正确的枚举类型
const eNodeType = nsMockManageApiManageMainCollectListGET.ENodeType;
const eEditable = nsApiManagerSearch.EEditable;

function renderCommonBox(iconNode: React.ReactNode, title: string, subTitle: string): React.ReactNode {
    return (
        <div className={css.commnonBox}>
            <div className={css.left}>{iconNode}</div>
            <div className={css.right}>
                <div className={css.title}>{title}</div>
                <div className={css.subTitle}>{subTitle}</div>
            </div>
        </div>
    );
}

function renderNewInterface(): React.ReactNode {
    const {
        catalogList,
        projectId
    } = useContext(ApiManageProjectInfoContext) as ApiManageProjectInfoContextType;
    const iconNode = (
        <div className={css.newInterfaceIconBox}>
            <KdevIconFont id={common_system_api} />
        </div>
    );
    const title = '新建接口';
    const subtitle = '支持对接口进行：接口测试、Mock、校验规则、代理配置、抓包等能力';
    return (
        <div
            className={css.newInterface}
            onClick={() => {
                router.push(ERouter.API_MOCK_REPO_EDIT_API,
                    {
                        projectCatalogId: catalogList[0].id, editType: 'new',
                        projectId
                    });
            }}
        >
            {renderCommonBox(iconNode, title, subtitle)}
        </div>
    );
}

function renderImportCurl(): React.ReactNode {
    const iconNode = (
        <div className={css.importCurlIconBox}>
            <KdevIconFont id={common_system_import} />
        </div>
    );
    const title = '导入 cURL';
    const subtitle = '支持快捷导入 cURL 文件，对导入的文件进行快捷测试';
    return (
        <div
            className={css.importCurl}
            onClick={() => {
                // 导入 cURL 逻辑
            }}
        >
            {renderCommonBox(iconNode, title, subtitle)}
        </div>
    );
}

function renderImportOriginRepo(): React.ReactNode {
    const importKapiRef = useRef<ImportRepoRef>(null);

    const iconNode = (
        <div className={css.importOriginRepoIconBox}>
            <KdevIconFont id={common_system_import} />
        </div>
    );
    const title = '导入原仓库目录接口';
    const subtitle = '支持快捷导入原仓库目录下的接口数据';
    return (
        <>
            <div
                className={css.importOriginRepo}
                onClick={() => {
                    importKapiRef.current?.open();
                }}
            >
                {renderCommonBox(iconNode, title, subtitle)}
            </div>
            <ImportRepo
                ref={importKapiRef}
            />
        </>
    );
}

function renderImportKapi(projectId: number): React.ReactNode {
    const importKapiRef = useRef<ImportKapiRef>(null);
    const iconNode = (
        <div className={css.importKapiIconBox}>
            <KdevIconFont id={common_system_import} />
        </div>
    );
    const title = '导入 KAPI 接口';
    const subtitle = '支持快捷导入 KAPI 接口数据';
    return (
        <>
            <div
                className={css.importCurl}
                onClick={() => {
                    importKapiRef.current?.open();
                }}
            >
                {renderCommonBox(iconNode, title, subtitle)}
            </div>
            <ImportKapi
                ref={importKapiRef}
                onSuccess={() => {
                    // 导入成功后的回调
                }}
                programId={projectId}
            />
        </>
    );
}

// 修改为React.FC类型
const EmptyProject: React.FC<{ projectId: number }> = ({ projectId }) => {
    return (
        <div className={css.projectInfoRight}>
            {renderNewInterface()}
            {/* {renderImportCurl()} */}
            {renderImportOriginRepo()}
            {renderImportKapi(projectId)}
        </div>
    );
}

export default function ProjectInfoRight() {
    const {
        catalogList,
        getCatalogList,
        selectedNode,
        setSelectedNode,
        catalogMap,
        setCatalogMap,
        setCatalogList,
        expandedKeys,
        setExpandedKeys,
        projectId,
        addTag
    } = useContext(ApiManageProjectInfoContext) as ApiManageProjectInfoContextType;

    const [search, setSearch] = useState<string>('');

    // 转换目录数据为 IItem[] 格式
    const convertToItemFormat = (
        data: nsMockManageApiManageMainApiProgramCatalog.ICatalogData[],
        parentId: number = 0
    ): nsApiManagerSearch.IItem[] => {
        return data.map(node => {
            const isApi = node.key?.startsWith('API-');
            return {
                id: node.id,
                name: node.name,
                parentId: node.parentId || parentId,
                apiId: isApi ? node.id : 0,
                groupId: !isApi ? node.id : 0,
                type: isApi ? eNodeType.API : eNodeType.GROUP,
                groupName: !isApi ? node.name : '',
                apiName: isApi ? node.name : '',
                children: node.children ? convertToItemFormat(node.children, node.id) : [],
                isLeaf: isApi,
                editable: eEditable.editable,
                follow: nsMockManageApiManageMainApiFollowPOST.EFollow.UN_FOLLOW,
                hasChild: node.hasChild || false,
                updateTime: node.updateTime || '',
                admin: [node.admin || {}],
                apiUrl: node.apiPath || '',
                apiSchema: node.apiSchema || '',
                priority: node.priority || '',
                tagList: node.tagList || ''
            };
        });
    };

    // 当点击一个目录或API时的处理函数
    const directoryTreeOnSelect = async (nodeData: nsApiManagerSearch.IItem) => {
        console.log('🔥 directoryTreeOnSelect 被调用了！', {
            nodeData,
            nodeType: nodeData.type,
            eNodeType,
            isGroup: nodeData.type === eNodeType.GROUP,
            isApi: nodeData.type === eNodeType.API
        });

        // 使用正确的枚举类型
        if (nodeData.type === eNodeType.GROUP) {
            // 构建完整的目录路径key
            const currentKey = selectedNode.key || '';
            const pathPart = currentKey.split('-').slice(1).join('-');
            const separator = currentKey ? '-' : '';
            const key = `CATALOG-${pathPart}${separator}${nodeData.groupId}`;

            const time = '' + +new Date();
            console.log('GROUP节点被点击，准备调用addTag:', {
                nodeData,
                key,
                time,
                addTag: typeof addTag
            });

            addTag({
                key: time,
                method: '', // GROUP 类型没有 method
                title: nodeData.groupName, // 使用 groupName 作为标题
                selectedKey: key,
                type: 'CATALOG',
                id: nodeData.groupId,
                isTemp: true
            });

            console.log('GROUP addTag调用完成');

            // 先更新选中节点
            setSelectedNode({
                id: nodeData.groupId,
                type: 'CATALOG',
                key: key
            });

            // 自动展开左侧目录树：补全所有父级key
            const parentKeys: string[] = [];
            const keyParts = key.split('-');
            // 只处理CATALOG类型的key
            for (let i = 2; i <= keyParts.length; i += 2) {
                parentKeys.push(keyParts.slice(0, i).join('-'));
            }
            const newExpandedKeys = Array.from(new Set([...(expandedKeys || []), ...parentKeys, key]));
            setExpandedKeys(newExpandedKeys);

            // 如果该节点有子节点但还未加载，则加载子节点数据
            const target = catalogMap[key];
            if (target && (!target.children || target.children.length === 0) && target.hasChild !== false) {
                try {
                    const res = await nsMockManageApiManageMainApiProgramCatalogSubList.remote({
                        id: nodeData.groupId
                    });
                    res.forEach(item => {
                        item.type = item.type.toUpperCase() as 'CATALOG' | 'API';
                    });

                    // 更新 catalogMap 和 catalogList
                    const newCatalogMap = { ...catalogMap };
                    const baseKey = `-${pathPart}${separator}${nodeData.groupId}-`;
                    const formattedChildren = formatCatalogList(res, baseKey, newCatalogMap);
                    target.children = formattedChildren;
                    setCatalogMap(newCatalogMap);
                    setCatalogList([...catalogList]);
                } catch (error) {
                    message.error('加载子目录失败');
                }
            }
        } else {
            // 构建完整的API路径key
            const currentKey = selectedNode.key || '';
            const pathPart = currentKey.split('-').slice(1).join('-');
            const separator = currentKey ? '-' : '';
            const key = `API-${pathPart}${separator}${nodeData.apiId}`;

            const time = '' + +new Date();

            console.log('API节点被点击，准备调用addTag:', {
                nodeData,
                key,
                time,
                addTag: typeof addTag
            });

            addTag({
                key: time,
                method: nodeData.apiSchema,
                title: nodeData.apiName,
                selectedKey: key,
                type: 'API',
                id: nodeData.apiId,
                isTemp: true
            });

            console.log('addTag调用完成');

            // setSelectedNode({
            //     id: nodeData.apiId,
            //     type: 'API',
            //     key: key
            // });

            // 新增：展开父级目录
            // key 形如 "API-1-2-3"，父级为 "CATALOG-1-2"
            // const keyParts = key.split('-');
            // if (keyParts.length > 2) {
            //     const parentKey = `CATALOG-${keyParts.slice(1, -1).join('-')}`;
            //     if (!expandedKeys.includes(parentKey)) {
            //         setExpandedKeys([...expandedKeys, parentKey]);
            //     }
            // }
        }
    };

    // 删除分组或API时的回调
    const onDeleteCallback = async (id: number) => {
        const node = findCatalogById(catalogList, id);
        await getCatalogList(node.key);
        setSelectedNode({ id: +node.key.split('-').pop()!, type: 'CATALOG', key: node.key });
    };

    // 移动API时的回调
    const onMoveCallback = async (key: string, curNodeData: any[]) => {
        // const curId = curNodeData[0].id;
        // // 定位父节点
        // const node = findCatalogById(catalogList, curId);
        // const parentId = node?.parentId;
        // const parantNode = findCatalogById(catalogList, parentId!);
        await getCatalogList(key);
        setSelectedNode({ id: +key.split('-').pop()!, type: 'CATALOG', key: key });
    };

    // 获取选中的节点key
    const selectedKey = selectedNode.key;
    const selectedData = selectedKey ? catalogMap[selectedKey] : null;

    // 渲染分组标题
    const renderGroupTitle = selectedData ? (
        <div style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 14 }}>
            {selectedData.name}
        </div>
    ) : null;

    // 根据选中状态渲染不同组件
    if (selectedNode.type === 'API') {
        return (
            <ViewApi
                apiId={selectedNode.id}
                extraUrlParams={{ catalogId: selectedData?.id, groupId: undefined, projectCatalogId: selectedData?.id }}
            />
        );
    } else if (selectedNode.type === 'CATALOG' && selectedData) {
        const convertedData = convertToItemFormat(selectedData.children || []);
        return (
            <GroupDetail
                projectId={projectId}
                viewType={'projectView'}
                directoryTreeOnSelect={directoryTreeOnSelect}
                data={convertedData}
                onMoveCallback={onMoveCallback}
                renderGroupTitle={renderGroupTitle}
                onDeleteCallback={onDeleteCallback}
                search={search}
                updateFun={getCatalogList}
                tableHiddenList={['checked']}
            />
        );
    } else {
        return <EmptyProject projectId={projectId} />;
    }
}
