import React, { useCallback, useContext, useEffect, useState, useRef } from 'react';
import css from './ProjectInfoLeft.less';
import { Button, Input, message, Spin, Tree, TreeSelect } from 'antd';
import { KdevIconFont, KEmpty } from '@/business/commonComponents';
import { common_system_arrowdownmian, common_system_overview, common_system_folder_expand1, common_system_folder_close_line, common_system_file_addition, common_system_folder_addition, common_system_splitterright } from '@kid/enterprise-icon/icon/output/icons';
import SearchProject from '../SearchProject/SearchProject';
import { ApiManageProjectInfoContext, ApiManageProjectInfoContextType, findCatalog, formatCatalogList } from '../ProjectInfo/ProjectInfo';
import { nsMockManageApiManageMainApiProgramCatalog, nsMockManageApiManageMainApiProgramCatalogSubList, nsMockManageApiManageMainApiProgramApi, nsMockManageApiManageMainApiProgramItemsSearch } from '@/remote';
import { DirectoryTreeNode } from '../../ApiDirectoryTree';
import RenderTreeTitle from '../RenderTreeTitle/RenderTreeTitle';
import { ConsoleSqlOutlined } from 'node_modules/@ant-design/icons/lib';
import { pushKey } from '@/index.config/tools';
import { debounce } from 'lodash';
import classnames from 'classnames';
import css2 from '../ProjectInfo/ProjectInfo.less';

import { ERouter } from 'CONFIG';
import { router } from '@libs/mvvm';
import ProjectInfoRight from '../ProjectInfoRight/ProjectInfoRight';
import { MethodTag } from '@/business/httpApiComponents/MethodTag';

let timer: any = null;
export default function ProjectInfoLeft() {
    const {
        currentGroupId,
        projectId,
        catalogList,
        setCatalogList,
        openCreateCatalogModal,
        changeGroup,
        getCatalogList,
        projectInfo,
        setProjectId,
        selectedNode,
        setSelectedNode,
        catalogMap,
        setCatalogMap,
        expandedKeys,
        setExpandedKeys,
        projectInfoTabs,
        addTag,
        setActiveKey
    } = useContext(ApiManageProjectInfoContext) as ApiManageProjectInfoContextType;

    const projectIdChange = (e: any) => {
        const id = e.detail;
        setProjectId(id);
        pushKey({
            projectId: id
        });
        router.push(ERouter.API_MOCK_REPO_MGR_PROJECT_INFO, { projectId: id, viewType: 'projectView' });
        console.log(22222)
        // setSelectedNode({ id: 0, type: null, key: null });
    }

    useEffect(() => {
        document.addEventListener('projectIdChange1', projectIdChange);
        return () => {
            document.removeEventListener('projectIdChange1', projectIdChange);
        }
    }, [])

    useEffect(() => {
        document.dispatchEvent(new CustomEvent('projectIdChange', {
            detail: {
                projectId,
                currentGroupId,
                projectInfo
            }
        }))
    }, [projectId, currentGroupId, projectInfo])

    // 树容器引用和高度状态
    const treeWrapRef = useRef<HTMLDivElement>(null);
    const treeRef = useRef<any>(null);
    const [treeWrapRefHeight, setTreeWrapRefHeight] = useState<number>(0);

    // 搜索相关状态
    const [searchTreeData, setSearchTreeData] = useState<any[]>([]);
    const [searchSelectedKey, setSearchSelectedKey] = useState<string>(null);
    const [searchCatalogMap, setSearchCatalogMap] = useState<
        Record<string, nsMockManageApiManageMainApiProgramCatalog.ICatalogData>
    >({});

    const onCreateCatalog = (parentId: number, parantName: string, id?: number, name?: string) => {
        openCreateCatalogModal(parentId, parantName, id, name);
    }

    const onDeleteCatalog = (id: number, key: string) => {
        const parentIdArr = key?.split('-') || [];
        const parentId = parentIdArr[parentIdArr.length - 2];
        nsMockManageApiManageMainApiProgramCatalog.deleteCatalog({
            id: id
        }).then(res => {
            message.success('删除成功');
            if (parentIdArr.length <= 2) {
                getCatalogList();
                pushKey({
                    selectedKey: ''
                });
                console.log(33333)
                setSelectedNode({ id: 0, type: null, key: null });
            } else {

                const parentKey = `CATALOG-${parentIdArr.slice(1, -1).join('-')}`;
                setSelectedNode({
                    id: Number(parentId),
                    type: 'CATALOG',
                    key: parentKey
                });
                pushKey({
                    selectedKey: parentKey
                });
                getCatalogList(parentKey);
            }
        });
    }
    const onDeleteApi = (id: number, key: string) => {
        const parentIdArr = key?.split('-') || [];
        const parentId = parentIdArr[parentIdArr.length - 2];
        nsMockManageApiManageMainApiProgramApi.remove({
            apiId: id,
            catalogId: Number(parentId)
        }).then(res => {
            message.success('删除成功');
            const parentKey = `CATALOG-${parentIdArr.slice(1, -1).join('-')}`;
            setSelectedNode({
                id: Number(parentId),
                type: 'CATALOG',
                key: parentKey
            });
            pushKey({
                selectedKey: parentKey
            });
            getCatalogList(parentKey);
        });
    }

    const onLoadData = async (info: any) => {
        const arr = info.key.split('-');
        const type = arr[0];
        const id = arr[arr.length - 1];
        if (type === 'CATALOG') {
            try {
                const res = await nsMockManageApiManageMainApiProgramCatalogSubList.remote(
                    { id: parseInt(id, 10) }
                );
                res.forEach(item => {
                    item.type = item.type.toUpperCase() as 'CATALOG' | 'API';
                });
                const target = findCatalog(catalogList, info.key);
                const baseKey = '-' + info.key.split('-').slice(1).join('-') + '-';
                const newCatalogMap = { ...catalogMap };
                if (target) {
                    target.children = formatCatalogList(res, baseKey, newCatalogMap);
                    setCatalogMap(newCatalogMap);
                    setCatalogList([...catalogList]);
                }
            } catch (error) {
                message.error('加载子目录失败');
            }
        }
    }


    // 搜索逻辑
    const handleSearch = async (value: string) => {
        if (timer) { clearTimeout(timer) }
        timer = setTimeout(async () => {
            if (!value) {
                setSearchTreeData([]);
                return;
            }
            try {
                const res = await nsMockManageApiManageMainApiProgramItemsSearch.remote({
                    programId: projectId,
                    key: value
                });
                const newCatalogMap: Record<string, nsMockManageApiManageMainApiProgramCatalog.ICatalogData> = {};
                const treeData = formatCatalogList(res, '-', newCatalogMap, true);
                setSearchCatalogMap(newCatalogMap);
                setSearchTreeData(treeData);
            } catch (e) {
                message.error('搜索失败');
            }
        }, 500)
    }

    // 选择主目录树节点
    // 判断两个元素是否相交
    const isIntersect = (oneNode: Element, twoNode: Element) => {
        const oneNodeRect = oneNode.getBoundingClientRect();
        const twoNodeRect = twoNode.getBoundingClientRect();
        const intersect = !(
            oneNodeRect.right < twoNodeRect.left ||
            oneNodeRect.left > twoNodeRect.right ||
            oneNodeRect.bottom < twoNodeRect.top ||
            oneNodeRect.top > twoNodeRect.bottom
        );
        return intersect;
    };

    // 监听容器高度变化
    useEffect(() => {
        if (treeWrapRef?.current) {
            setTreeWrapRefHeight(treeWrapRef.current.clientHeight);
            const resizeObserver = new ResizeObserver(entries => {
                requestAnimationFrame(() => {
                    setTreeWrapRefHeight(entries[0].contentRect.height);
                })
            });
            resizeObserver.observe(treeWrapRef.current);
            return () => {
                resizeObserver.disconnect();
            };
        }
    }, [treeWrapRef?.current]);

    // 滚动到选中节点
    useEffect(() => {
        let timeId: number;
        const scrollToSelectKey = () => {
            selectedNode.key && treeRef.current?.scrollTo({ key: selectedNode.key, align: 'top' } as any);
        };

        // 若选中元素不在视口内，则让其滚动到顶部
        const antTreeNodeSelected = treeWrapRef.current?.querySelector('.ant-tree-node-selected');
        if (antTreeNodeSelected && treeWrapRef.current &&
            !isIntersect(treeWrapRef.current as Element, antTreeNodeSelected)) {
            timeId = window.setTimeout(scrollToSelectKey, 500);
        }

        return () => {
            clearTimeout(timeId);
        };
    }, [selectedNode.key]);

    const onSelect = async (keys: React.Key[], info: any) => {
        localStorage.setItem('REJECT_SELECT', info.node.key);
        if (keys.length === 0) return;

        const selectedKey = keys[0] as string;
        const keyParts = selectedKey.split('-');
        const type = keyParts[0];
        const id = parseInt(keyParts[keyParts.length - 1], 10);

        // 如果是目录类型，且没有加载过子目录（children为空数组或未定义），则加载子目录
        if (type === 'CATALOG') {
            const target = findCatalog(catalogList, selectedKey);
            if (target && (!target.children || target.children.length === 0) && target.hasChild !== false) {
                try {
                    const res = await nsMockManageApiManageMainApiProgramCatalogSubList.remote({ id });
                    res.forEach(item => {
                        item.type = item.type.toUpperCase() as 'CATALOG' | 'API';
                    });
                    const baseKey = '-' + selectedKey.split('-').slice(1).join('-') + '-';
                    const newCatalogMap = { ...catalogMap };
                    const formattedChildren = formatCatalogList(res, baseKey, newCatalogMap);
                    target.children = formattedChildren;
                    setCatalogMap(newCatalogMap);
                    setCatalogList([...catalogList]);
                } catch (error) {
                    message.error('加载子目录失败');
                }
            }
        }
        const time = '' + +new Date();
        addTag({
            key: time,
            method: info.node.apiSchema,
            title: info.node.title,
            selectedKey,
            type: type as 'CATALOG' | 'API',
            id,
            isTemp: true
        });
        setTimeout(() => {
            setActiveKey(time);
        }, 0)
        // pushKey({
        //     selectedKey: info.node.key
        // });
        // setSelectedNode({
        //     id,
        //     type: type as 'CATALOG' | 'API',
        //     key: selectedKey
        // });
    };

    return <div className={css.projectInfoLeft}>
        <div className={css.header}>
            <div className={css.title}>
                <span>接口目录</span>
            </div>
            {/* <div className={css.divider}>
                <span><KdevIconFont id={common_system_splitterright} style={{ fontSize: 16, color: '#D5D6D9ff' }} /></span>
            </div>
            <div className={css.projectName}>
                <SearchProject
                    projectName={projectInfo?.name || ''}
                    onChange={(id: number) => {
                       
                    }}
                    currentGroupId={currentGroupId}
                    currentProjectId={projectId}
                />
            </div> */}
        </div>
        <div className={css.search}>
            <TreeSelect
                showSearch
                placeholder="请输入目录或API名称"
                style={{ flex: 1 }}
                value={searchSelectedKey}
                onSearch={(v) => {
                    setSearchSelectedKey(v);
                    handleSearch(v)
                }}
                treeDefaultExpandAll
                popupClassName={css.searchTreeResult}
                dropdownStyle={{
                    padding: 0,
                    zIndex: 100
                }}
                dropdownRender={(menu) => {
                    if (searchTreeData.length === 0) {
                        return <KEmpty
                            image="NOMAL_SIMPLE_SEARCH"
                            description="本团队目录下，暂无数据"
                            className={css.kEmpty}
                        />
                    }
                    return <Tree.DirectoryTree
                        treeData={searchTreeData}
                        className={css.repoDirectoryTree}
                        icon={false}
                        autoExpandParent={false}
                        defaultExpandAll
                        expandedKeys={Object.keys(searchCatalogMap)}
                        onSelect={async (keys, info) => {
                            await getCatalogList(info.node.key)
                            onSelect(keys, info);
                        }}
                        // selectedKeys={selectedNode.key ? [selectedNode.key] : []}
                        titleRender={(nodeData) => {
                            return <RenderTreeTitle
                                nodeData={nodeData}
                                expandedKeys={expandedKeys}
                                searchSelectedKey={searchSelectedKey}
                                onCreateCatalog={(name) => { onCreateCatalog(nodeData.id, name) }}
                                onEditCatalog={(name) => {
                                    const arr = nodeData.key.split('-');
                                    const parentId = arr[arr.length - 2];
                                    const parent = findCatalog(catalogList, arr.slice(0, -1).join('-'));
                                    onCreateCatalog(Number(parentId), parent?.name || '', nodeData.id, name)
                                }}
                                onDeleteCatalog={() => { onDeleteCatalog(nodeData.id, nodeData.key) }}
                                onDeleteApi={() => { onDeleteApi(nodeData.id, nodeData.key) }}
                            />
                        }}
                        height={300} // 为搜索结果树设置固定高度
                    />
                }}
            />
            <Button
                type="primary"
                style={{ flexShrink: 0, marginLeft: '8px' }}
                icon={<KdevIconFont id={'#iconadd'} />}
                onClick={() => {
                    openCreateCatalogModal(0, '');
                }}
            />
        </div>
        <div className={css.projectList} ref={treeWrapRef}>
            {/* <div className={css.projectTitle}>
                <KdevIconFont style={{ color: '#252626ff' }} id={common_system_overview} />
                <span>项目概览</span>

                <div onClick={() => {
                    openCreateCatalogModal(0, '');
                }}>
                    <KdevIconFont className={css.addIcon}
                        style={{ color: '#252626ff' }} id={common_system_folder_addition}
                    />
                </div>
            </div> */}

            <div className={css.projectTree} >
                <Spin spinning={false}>
                    <Tree.DirectoryTree
                        ref={treeRef}
                        treeData={catalogList}
                        className={css.repoDirectoryTree}
                        icon={false}
                        expandAction={'doubleClick'}
                        autoExpandParent={false}
                        expandedKeys={expandedKeys}
                        loadedKeys={expandedKeys}
                        selectedKeys={selectedNode.key ? [selectedNode.key] : []}
                        onSelect={onSelect}
                        titleRender={(nodeData) => {
                            return <RenderTreeTitle
                                nodeData={nodeData}
                                expandedKeys={expandedKeys}
                                onCreateCatalog={(name) => { onCreateCatalog(nodeData.id, name) }}
                                onEditCatalog={(name) => {
                                    const arr = nodeData.key.split('-');
                                    const parentId = arr[arr.length - 2];
                                    const parent = findCatalog(catalogList, arr.slice(0, -1).join('-'));
                                    onCreateCatalog(Number(parentId), parent?.name || '', nodeData.id, name)
                                }}
                                onDeleteCatalog={() => { onDeleteCatalog(nodeData.id, nodeData.key) }}
                                onDeleteApi={() => { onDeleteApi(nodeData.id, nodeData.key) }}
                            />
                        }}
                        onExpand={(newExpandedKeys, info) => {
                            setExpandedKeys(newExpandedKeys as string[]);
                        }}
                        loadData={onLoadData}
                        height={treeWrapRefHeight}
                    />
                </Spin>
            </div>
        </div>
    </div>
}
