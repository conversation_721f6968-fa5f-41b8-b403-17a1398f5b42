import React, { useState, useEffect, createContext } from 'react'
import classNames from 'classnames'
import css from './ProjectSetting.less'
import { KdevIconFont } from '@/business/commonComponents'
import { common_system_arrowleft02 } from '@kid/enterprise-icon/icon/output/icons'
import { getUrlSearch, pushKey } from '@/index.config/tools'
import { router } from '@libs/mvvm'
import { ERouter } from 'CONFIG'

// 导入各个设置组件
import BasicInfo from './BasicInfo/BasicInfo'
import MemberManage from './MemberManage/MemberManage'
import EnvSettings from './EnvSettings/EnvSettings'
import TagSettings from './TagSettings/TagSettings'
import RepoSync from './RepoSync/RepoSync'
import ProjectInfo from '../ProjectInfo/ProjectInfo'
import { nsMockManageApiManageMainApiProgram } from '@/remote'

enum MenuType {
    BASIC_INFO = 'basicInfo',
    MEMBER_MANAGE = 'memberManage',
    ENV_SETTINGS = 'envSettings',
    TAG_SETTINGS = 'tagSettings',
    REPO_SYNC = 'repoSync'
}

const VALID_MENUS = Object.values(MenuType)

export interface ApiManageProjectSettingContextType {
    projectInfo: nsMockManageApiManageMainApiProgram.IReturn | null;
    setProjectInfo: (projectInfo: nsMockManageApiManageMainApiProgram.IReturn) => void;
}
export const ApiManageProjectInfoContext = createContext<ApiManageProjectSettingContextType>({});
const ProjectSetting: React.FC = () => {
    const [projectInfo, setProjectInfo] = useState<nsMockManageApiManageMainApiProgram.IReturn | null>(null);
    const [activeMenu, setActiveMenu] = useState<MenuType>(MenuType.BASIC_INFO);


    useEffect(() => {
        const params = getUrlSearch(['projectId']) as { projectId: number, menu: MenuType };

        // 从URL中获取当前选中的菜单
        const menuFromUrl = params.menu
        if (menuFromUrl && VALID_MENUS.includes(menuFromUrl as MenuType)) {
            setActiveMenu(menuFromUrl as MenuType)
        }

        // 获取项目信息
        nsMockManageApiManageMainApiProgram.remote({ id: params.projectId }).then((res) => {
            setProjectInfo(res);
            document.dispatchEvent(new CustomEvent('projectIdChange', {
                detail: {
                    projectId: params.projectId,
                    projectInfo: res
                }
            }));
        })
    }, [])

    const handleGoBack = () => {
        router.push(ERouter.API_MOCK_REPO_MGR)
    }

    const handleMenuClick = (menuKey: MenuType) => {
        setActiveMenu(menuKey)
        // 更新URL参数
        pushKey({ menu: menuKey })
    }

    // 根据选中的菜单项渲染对应的组件
    const renderContent = () => {
        switch (activeMenu) {
            case MenuType.BASIC_INFO:
                return <BasicInfo />
            case MenuType.MEMBER_MANAGE:
                return <MemberManage />
            case MenuType.ENV_SETTINGS:
                return <EnvSettings />
            case MenuType.TAG_SETTINGS:
                return <TagSettings />
            case MenuType.REPO_SYNC:
                return <RepoSync />
            default:
                return <BasicInfo />
        }
    }

    return (
        <ApiManageProjectInfoContext.Provider
            value={{
                projectInfo,
                setProjectInfo
            }}>
            <div className={css.container}>
                {/* 顶部header区域 */}
                <div className={css.header}>
                    {/* <div className={css.backBtn} onClick={handleGoBack}>
                        <KdevIconFont id={common_system_arrowleft02} style={{ color: '#898A8Cff' }} />
                        <span className={css.backText}>返回</span>
                    </div> 
                    <div className={css.line}></div> */}
                    <div className={css.projectName}>项目设置</div>
                </div>
                <div className={css.content}>
                    {/* 左侧区域 */}
                    <div className={css.sidebar}>
                        <div className={css.menuContainer}>
                            {/* 通用设置 */}
                            <div className={css.parentMenu}>通用设置</div>
                            <div
                                className={classNames(css.subMenu, {
                                    [css.active]: activeMenu === MenuType.BASIC_INFO
                                })}
                                onClick={() => handleMenuClick(MenuType.BASIC_INFO)}
                            >
                                基本信息
                            </div>
                            {/* <div
                                className={classNames(css.subMenu, {
                                    [css.active]: activeMenu === MenuType.MEMBER_MANAGE
                                })}
                                onClick={() => handleMenuClick(MenuType.MEMBER_MANAGE)}
                            >
                                成员管理
                            </div> */}

                            {/* 公共设置 */}
                            <div className={css.parentMenu}>公共设置</div>
                            <div
                                className={classNames(css.subMenu, {
                                    [css.active]: activeMenu === MenuType.ENV_SETTINGS
                                })}
                                onClick={() => handleMenuClick(MenuType.ENV_SETTINGS)}
                            >
                                环境设置
                            </div>
                            <div
                                className={classNames(css.subMenu, {
                                    [css.active]: activeMenu === MenuType.TAG_SETTINGS
                                })}
                                onClick={() => handleMenuClick(MenuType.TAG_SETTINGS)}
                            >
                                标签设置
                            </div>
                            {/* <div
                                className={classNames(css.subMenu, {
                                    [css.active]: activeMenu === MenuType.REPO_SYNC
                                })}
                                onClick={() => handleMenuClick(MenuType.REPO_SYNC)}
                            >
                                仓库同步设置
                            </div> */}
                        </div>
                    </div>
                    {/* 右侧内容区域 */}
                    <div className={css.mainContent}>
                        {renderContent()}
                    </div>
                </div>
            </div>
        </ApiManageProjectInfoContext.Provider>
    )
}

export default ProjectSetting
