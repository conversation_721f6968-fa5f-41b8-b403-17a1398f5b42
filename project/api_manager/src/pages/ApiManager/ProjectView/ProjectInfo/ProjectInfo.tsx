import React, { useState, useEffect, createContext, useRef, useCallback } from 'react';
import type { TabsProps } from 'antd';
import SplitPane from 'react-split-pane';
import Pane from 'react-split-pane/lib/Pane';
import { useLocalStorageState } from 'ahooks';
import classNames from 'classnames';
import css from './ProjectInfo.less';
import { getUrlSearch, pushKey } from '@/index.config/tools';
import ProjectInfoLeft from '../ProjectInfoLeft/ProjectInfoLeft';
import ProjectInfoRight from '../ProjectInfoRight/ProjectInfoRight';
import { getGroup } from '../ProjectViewList/ProjectViewList';
import { message, Modal, Tabs } from 'antd';
import { nsMockManageApiManageMainApiProgramCatalogRootList, nsMockManageApiManageMainApiProgramCatalog, nsMockManageApiManageMainApiProgramCatalogSubList, nsMockManageApiManageMainApiProgramList, nsMockManageApiManageMainApiProgram } from '@/remote';
import { CreateCatalog, CreateCatalogRef } from '../CreateCatalog/CreateCatalog';
import { ERouter } from 'CONFIG';
import { router } from '@libs/mvvm';
import { MethodTag } from '@/business/httpApiComponents/MethodTag';
import { KdevIconFont } from '@/business/commonComponents';
import { common_system_closesmall } from '@kid/enterprise-icon/icon/output/icons';
import { constantNumericComplex } from 'ace-builds/src-noconflict/mode-ruby_highlight_rules';
// import { Sticky, StickyContainer } from 'react-sticky';

const PROJECT_INFO_TABS = 'PROJECT_INFO_TABS';
const PROJECT_INFO_ACTIVE_KEY = 'PROJECT_INFO_ACTIVE_KEY';

let lastClickKey = '';


window.ProjectInfoTabs = window.ProjectInfoTabs || JSON.parse(localStorage.getItem(PROJECT_INFO_TABS) || '[]') || [];
window.ActiveKey = window.ActiveKey || localStorage.getItem(PROJECT_INFO_ACTIVE_KEY) || '';

interface IProps { }

interface MenuItemType extends nsMockManageApiManageMainApiProgramCatalogRootList.IReturn_Item {
    children?: MenuItemType[],
    key?: string,
    title?: string,
    isLeaf?: boolean
}

export function formatCatalogList(
    catalogList: MenuItemType[],
    baseName: string = '-',
    catalogMap: Record<string, nsMockManageApiManageMainApiProgramCatalog.ICatalogData> = {},
    isSearch: boolean = false
) {
    const formattedList = catalogList.map(item => {
        const formattedItem = {
            ...item,
            key: (item.type || 'CATALOG') + baseName + item.id,
            title: item.name,
            children: formatCatalogList(item.children || [], baseName + item.id + '-', catalogMap, isSearch),
            isLeaf: isSearch ? !item.children?.length : item.type === 'API',
            hasChild: item.hasChild === undefined ? true : item.hasChild
        };
        // 将当前节点添加到 catalogMap
        catalogMap[formattedItem.key] = formattedItem;
        return formattedItem;
    });
    return formattedList;
}
export function findCatalog(
    catalogList: nsMockManageApiManageMainApiProgramCatalog.ICatalogData[],
    key: string
) {
    const arr = key.split('-');
    const type = arr[0];
    const id = arr[arr.length - 1];
    const pathArrOuter = arr.slice(1);
    return inner(catalogList, pathArrOuter);
    function inner(list: nsMockManageApiManageMainApiProgramCatalog.ICatalogData[], pathArr: string[]) {
        if (list.length === 0) {
            return null;
        }
        const firstTarget = pathArr[0];
        const nextTarget = pathArr.slice(1);
        for (const element of list) {
            if (element.id === parseInt(firstTarget, 10)) {
                if (nextTarget.length > 0) {
                    return inner(element.children || [], nextTarget);
                } else {
                    return element;
                }
            }
        }
        return null
    }
}
export function findCatalogById(
    catalogList: nsMockManageApiManageMainApiProgramCatalog.ICatalogData[],
    id: number
) {
    for (const item of catalogList) {
        if (item.id === id) {
            return item;
        } else if (item.children) {
            const result = findCatalogById(item.children, id);
            if (result) {
                return result;
            }
        }
    }
    return null;
}

// 一个选中节点类型
export interface SelectedNode {
    id: number;
    type: 'CATALOG' | 'API' | null;
    key: string | null;
}

// 标签类型
export interface ProjectInfoTabsType {
    key: string,
    method?: string,
    title: string,
    selectedKey?: string,
    type?: string,
    id?: number,
    isTemp: boolean
}

export function getNewProjectInfoTab(): ProjectInfoTabsType {
    return {
        key: '' + +new Date(),
        title: '新建标签',
        isTemp: true
    }
}
// export function getApiProjectInfoTab(key: string, method: string, title: string, selectedKey: string, type: string, id: number): ProjectInfoTabsType {
//     return {
//         key,
//         method,
//         title,
//         selectedKey,
//         type,
//         id,
//         isTemp: true
//     }
// }

declare global {
    interface Window {
        ProjectInfoTabs: ProjectInfoTabsType[];
        ActiveKey: string;
    }
}

const renderTabBar: TabsProps['renderTabBar'] = (props, DefaultTabBar) => {
    return (
        <DefaultTabBar {...props} />
    )
};

// 上下文类型
export interface ApiManageProjectInfoContextType {
    currentGroupId: number;
    projectId: number;
    setProjectId: (projectId: number) => void;
    catalogList: nsMockManageApiManageMainApiProgramCatalog.ICatalogData[];
    setCatalogList: (catalogList: nsMockManageApiManageMainApiProgramCatalog.ICatalogData[]) => void;
    openCreateCatalogModal: (parentId: number, parantName: string, editId?: number, editName?: string) => void;
    changeGroup: () => void;
    getCatalogList: (selectedKey?: string) => void;
    projectInfo: nsMockManageApiManageMainApiProgram.IReturn | undefined;
    selectedNode: SelectedNode;
    setSelectedNode: (node: SelectedNode) => void;
    catalogMap: Record<string, nsMockManageApiManageMainApiProgramCatalog.ICatalogData>;
    setCatalogMap: (map: Record<string, nsMockManageApiManageMainApiProgramCatalog.ICatalogData>) => void;
    expandedKeys: string[],
    setExpandedKeys: (arr: string[]) => void,
    currentFirstGroupId: number,
    projectInfoTabs: ProjectInfoTabsType[],
    addTag: (tabs: ProjectInfoTabsType) => void,
    activeKey: string,
    setActiveKey: (key: string) => void,
    setProjectInfoTabs: (tabs: ProjectInfoTabsType[]) => void
    // onTabActiveChange: (key: string) => void
}

// 上下文默认值
const contextDefaultValue: ApiManageProjectInfoContextType = {} as ApiManageProjectInfoContextType;

// 上下文
export const ApiManageProjectInfoContext = createContext<ApiManageProjectInfoContextType>(contextDefaultValue);

export default function ProjectInfo(props: IProps) {
    const searchParams = getUrlSearch(['projectId']) as {
        projectId: number, selectedKey?: string, tryToDelete?: string
    };
    const [projectId, setProjectId] = useState<number>(searchParams.projectId);
    const [projectInfo, setProjectInfo] = useState<nsMockManageApiManageMainApiProgram.IReturn>();
    const [leftPaneSize, setLeftPaneSize] = useLocalStorageState<string>('project_info_leftpane_size', {
        defaultValue: '314px'
    });
    // 当前分组ID
    const [currentGroupId, setCurrentGroupId] = useState<number>(0);
    // 当前一级分组名称
    const [currentFirstGroupId, setCurrentFirstGroupId] = useState<number>(0);
    // 目录列表
    const [catalogList, setCatalogList] = useState<nsMockManageApiManageMainApiProgramCatalog.ICatalogData[]>([]);
    // 添加catalogMap状态
    const [catalogMap, setCatalogMap] = useState<
        Record<string, nsMockManageApiManageMainApiProgramCatalog.ICatalogData>
    >({});
    // 新建目录弹框
    const [isCreateCatalogModalOpen, setIsCreateCatalogModalOpen] = useState<boolean>(false);
    // 新建目录弹框ref
    const createCatalogRef = useRef<CreateCatalogRef>(null);
    const [createCatalogLoading, setCreateCatalogLoading] = useState<boolean>(false);
    // 新增：编辑模式下的目录id
    const [editCatalogId, setEditCatalogId] = useState<number | null>(null);

    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);


    // 添加选中节点状态
    const [selectedNode, setSelectedNode] = useState<SelectedNode>({ id: 0, type: null, key: null });

    // 获取分组ID并赋值
    const changeGroup = async () => {
        const group = await getGroup();
        setCurrentGroupId(group.groupId);
        setCurrentFirstGroupId(group.parentId === -1 ? group.groupId : group.parentId);
        router.push(ERouter.API_MOCK_REPO_MGR, { viewType: 'projectView' });
    }
    // 获取分组ID并赋值
    const initChangeGroup = async () => {
        const group = await getGroup();
        setCurrentGroupId(group.groupId);
        setCurrentFirstGroupId(group.parentId === -1 ? group.groupId : group.parentId);
    }

    const getCatalogList = async (selectedKey?: string) => {
        if (!selectedKey) {
            selectedKey = undefined;
        }
        const arr = selectedKey?.split('-');
        const locationId = arr ? parseInt(arr[arr.length - 1], 10) : 0;
        const locationType = arr ? arr[0] : undefined;
        setExpandedKeys([]);
        const res = await nsMockManageApiManageMainApiProgramCatalogRootList.remote({
            programId: projectId,
            locationId,
            locationType
        });
        const newCatalogMap: Record<string, nsMockManageApiManageMainApiProgramCatalog.ICatalogData> = {};
        let formattedList = formatCatalogList(res, '-', newCatalogMap);
        // 新增逻辑：如果locationId存在且locationType为CATALOG，获取其子目录并更新到catalogList
        if (locationId && locationType === 'CATALOG') {
            try {
                const subList = await nsMockManageApiManageMainApiProgramCatalogSubList.remote({ id: locationId });
                // 找到对应节点并更新children
                const updateChildren = (list: any[]): any[] => {
                    return list.map(item => {
                        if (item.id === locationId && item.type === 'CATALOG') {
                            // 合并子目录
                            const newItem = {
                                ...item,
                                children: formatCatalogList(subList, `-${arr?.slice(1).join('-')}-`, newCatalogMap)
                            };
                            newCatalogMap[newItem.key] = newItem;
                            return newItem;
                        } else if (item.children) {
                            const newItem = {
                                ...item,
                                children: updateChildren(item.children)
                            };
                            newCatalogMap[newItem.key] = newItem;
                            return newItem;
                        }
                        newCatalogMap[item.key] = item;
                        return item;
                    });
                };
                formattedList = updateChildren(formattedList);
            } catch (e) {
                // 可以根据需要添加错误提示
                console.error('获取子目录失败', e);
            }
        }
        setCatalogMap({ ...newCatalogMap }); // 保证引用变化
        setCatalogList(formattedList);
        const node = findCatalogById(formattedList, locationId);
        const nodeKey = node?.key.split('-');
        if (nodeKey) {
            const result: string[] = [];
            let temp = '';
            for (const item of nodeKey) {
                if (!temp) {
                    temp += 'CATALOG';
                } else {
                    temp += `-${item}`;
                }
                result.push(temp);
            }
            setExpandedKeys(() => result);
            if (searchParams.tryToDelete === 'true') {
                const parantNode = findCatalogById(formattedList, node.parentId);
                setSelectedNode({
                    id: parantNode?.id,
                    type: parantNode?.type,
                    key: parantNode.key!
                });
                requestAnimationFrame(() => {
                    document.dispatchEvent(new CustomEvent('tryToDelete', {
                        detail: {
                            node: {
                                apiId: node.id,
                                parentId: parantNode.id,
                                type: node.type === 'API' ? 1 : 0
                            }
                        }
                    }));
                    pushKey({}, ['tryToDelete'])
                });
            } else {
                const time = '' + +new Date();
                if (!projectInfoTabs.find(a => a.selectedKey === node.key)) {
                    addTag({
                        key: time,
                        method: node.apiSchema,
                        title: node.title,
                        selectedKey: node.key,
                        type: node.type,
                        id: node.id,
                        isTemp: true
                    });
                    setActiveKey(time);
                }
                setSelectedNode({
                    id: node?.id,
                    type: node?.type,
                    key: node.key!
                });
            }
        }
    }
    const getProjectInfo = async () => {
        const res = await nsMockManageApiManageMainApiProgram.remote({ id: projectId });
        setProjectInfo(res);
    }

    const changeProjectId = (e: any) => {
        console.log(projectId, 'projectId')
        setProjectId(e.detail.projectId);
        console.log(11111)
        // setSelectedNode({ id: 0, type: null, key: null });
    }
    useEffect(() => {
        document.addEventListener('projectIdChange', changeProjectId);
        return () => {
            document.removeEventListener('projectIdChange', changeProjectId);
        }
    }, [])

    // 获取根目录
    useEffect(() => {
        getCatalogList(searchParams.selectedKey);
        getProjectInfo();
    }, [projectId])
    // 监听分组改变
    useEffect(() => {
        document.addEventListener('selectedGroup', changeGroup);
        initChangeGroup();
        return () => {
            document.removeEventListener('selectedGroup', changeGroup);
        }
    }, [])
    const onChangePaneSize = (paneSize: any) => {
        setLeftPaneSize(paneSize[0]);
    };

    const handleCreateCatalogModalCancel = () => {
        setIsCreateCatalogModalOpen(false);
    }

    const handleCreateCatalogModalOk = () => {
        const data = createCatalogRef.current?.getParams();
        setCreateCatalogLoading(true);
        if (editCatalogId) {
            // 编辑模式
            nsMockManageApiManageMainApiProgramCatalog.updateCatalog({
                id: editCatalogId,
                name: data.name,
                desc: ''
            }).then(res => {
                setIsCreateCatalogModalOpen(false);
                setEditCatalogId(null);
                setCreateCatalogLoading(false);
                message.success('目录编辑成功');
                const node = findCatalogById(catalogList, editCatalogId!);
                getCatalogList(node.key);
            }).catch(() => {
                setCreateCatalogLoading(false);
            });
        } else {
            // 新建模式
            nsMockManageApiManageMainApiProgramCatalog.createCatalog({
                name: data.name,
                desc: '',
                programId: projectId,
                parentId: data.parantId
            }).then(res => {
                setIsCreateCatalogModalOpen(false);
                setCreateCatalogLoading(false);
                message.success('目录创建成功');
                if (data.parantId) {
                    const node = findCatalogById(catalogList, data.parantId);
                    getCatalogList(node.key);
                } else {
                    getCatalogList();
                }
            }).catch(() => {
                setCreateCatalogLoading(false);
            });
        }
    }
    const openCreateCatalogModal = (parentId: number, parantName: string, editId?: number, editName?: string) => {
        setIsCreateCatalogModalOpen(true);
        setEditCatalogId(editId || null);
        requestAnimationFrame(() => {
            createCatalogRef.current?.setFormData({
                name: editName || '',
                parantId: parentId || 0,
                parantName: parantName,
                programId: editId
            });
        })
    }

    const [projectInfoTabs, setProjectInfoTabs] = useState<ProjectInfoTabsType[]>(window.ProjectInfoTabs);
    const [activeKey, setActiveKey] = useState(window.ActiveKey || projectInfoTabs[0]?.key);

    useEffect(() => {
        window.ProjectInfoTabs = projectInfoTabs;
        localStorage.setItem(PROJECT_INFO_TABS, JSON.stringify(projectInfoTabs));
    }, [projectInfoTabs])

    useEffect(() => {
        window.ActiveKey = activeKey;
        activeKey && localStorage.setItem(PROJECT_INFO_ACTIVE_KEY, activeKey);
    }, [activeKey])

    useEffect(() => {
        if (!searchParams.selectedKey && !projectInfoTabs.length) {
            setProjectInfoTabs([...projectInfoTabs, getNewProjectInfoTab()])
        }
    }, [])

    const onTabActiveChange = useCallback((newActiveKey: string) => {
        const tab = projectInfoTabs.find(item => item.key === newActiveKey);
        if (tab?.selectedKey) {
            pushKey({
                selectedKey: tab.selectedKey,
            });
            setSelectedNode({
                id: tab.id as number,
                type: tab.type as 'CATALOG' | 'API',
                key: tab.selectedKey as string
            });
        } else {
            pushKey({}, ['selectedKey']);
            setSelectedNode({
                id: 0,
                type: null,
                key: null
            });
        }
        setActiveKey(newActiveKey);
    }, [projectInfoTabs]);

    useEffect(() => {
        onTabActiveChange(activeKey);
    }, [activeKey])

    const add = () => {
        const newPanes = [...projectInfoTabs.map(i => ({ ...i, isTemp: false }))];
        const newTab = getNewProjectInfoTab();
        newPanes.push(newTab);
        setProjectInfoTabs(newPanes);
        setActiveKey(newTab.key);
    };

    const addTag = useCallback((item: ProjectInfoTabsType) => {
        console.log(item, 'item')
        setProjectInfoTabs(prevTabs => {
            const hasTemp = prevTabs.findIndex(a => a.isTemp);
            console.log(hasTemp, 'hasTemp')
            if (hasTemp !== -1) {
                // 替换临时标签
                const newTabs = [...prevTabs];
                newTabs[hasTemp] = item;
                console.log('替换临时标签:', newTabs);
                return newTabs;
            } else {
                // 添加新标签
                const newTabs = [...prevTabs, item];
                console.log('添加新标签:', newTabs);
                return newTabs;
            }
        });
    }, [])

    const remove = (targetKey: string) => {
        let newActiveKey = activeKey;
        let lastIndex = -1;
        projectInfoTabs.forEach((item, i) => {
            if (item.key === targetKey) {
                lastIndex = i - 1;
            }
        });
        const newPanes = projectInfoTabs.filter(item => item.key !== targetKey);
        if (newPanes.length && newActiveKey === targetKey) {
            if (lastIndex >= 0) {
                newActiveKey = newPanes[lastIndex].key;
            } else {
                newActiveKey = newPanes[0].key;
            }
        }
        setProjectInfoTabs(newPanes);
        setActiveKey(newActiveKey);
    };

    const onEdit = (targetKey: string | React.MouseEvent | React.KeyboardEvent, action: 'add' | 'remove') => {
        if (action === 'add') {
            add();
        } else {
            remove(targetKey as string);
        }
    };

    const setNoTemp = (key: string) => {
        const newPanes = [...projectInfoTabs];
        const tab = newPanes.find(item => item.key === key);
        if (tab) {
            tab.isTemp = false;
        }
        setProjectInfoTabs(newPanes);
    }

    const formatTab = useCallback((arr: any[]) => {
        const res = arr.map(item => {
            const { method, title, selectedKey, type, id, key, isTemp } = item;
            if (item.id) {
                return {
                    label: <div className={classNames(css.tabTitle, isTemp && css.italic)}>
                        <div className={css.tabIcon}><MethodTag method={method}></MethodTag></div>
                        <div className={css.tabTitleText}>{title}</div>
                    </div>,
                    data: {
                        selectedKey,
                        type,
                        id
                    },
                    children: <div style={{ height: '100%' }} onClick={() => {
                        setNoTemp(key);
                    }} ><ProjectInfoRight /></div>,
                    key,
                    isTemp
                }
            } else {
                return {
                    label: <div className={classNames(css.tabTitle, isTemp && css.italic)}>
                        <div className={classNames(css.tabTitleText)}>新建标签</div>
                    </div>,
                    data: {},
                    children: <div style={{ height: '100%' }} onClick={() => {
                        setNoTemp(key);
                    }} ><ProjectInfoRight /></div>,
                    key,
                    isTemp
                }
            }
        })
        return res
    }, [projectInfoTabs]);

    console.log('🚀 ProjectInfo 渲染，当前 projectInfoTabs:', projectInfoTabs, 'length:', projectInfoTabs.length)

    return (
        <>
            <ApiManageProjectInfoContext.Provider
                value={{
                    currentGroupId,
                    projectId,
                    setProjectId,
                    catalogList,
                    setCatalogList,
                    openCreateCatalogModal,
                    changeGroup,
                    getCatalogList,
                    projectInfo,
                    selectedNode,
                    setSelectedNode,
                    catalogMap,
                    setCatalogMap,
                    expandedKeys,
                    setExpandedKeys,
                    currentFirstGroupId,
                    projectInfoTabs,
                    addTag,
                    activeKey,
                    setActiveKey,
                    setProjectInfoTabs
                }}
            >
                <div className={css.projectInfo}>
                    <SplitPane
                        onChange={onChangePaneSize}
                        allowResize={true}
                        className={css.splitPaneResizer}
                    >
                        <Pane
                            size={leftPaneSize}
                            minSize={'272px'}
                            maxSize="600px"
                            style={{ visibility: 'visible' }}
                        >
                            <ProjectInfoLeft />
                        </Pane>
                        <Pane className={css.rightPane}>
                            <div className={css.tabsContainer}>
                                <span style={{ color: 'red', fontWeight: 'bold' }}>标签数量: {projectInfoTabs.length}</span>
                                <Tabs
                                    style={{ height: '100%' }}
                                    type="editable-card"
                                    onChange={onTabActiveChange}
                                    activeKey={activeKey}
                                    onEdit={onEdit}
                                    items={formatTab(projectInfoTabs)}
                                    renderTabBar={renderTabBar}
                                    destroyInactiveTabPane={true}
                                    onTabClick={(e) => {
                                        if (lastClickKey === e) {
                                            setNoTemp(e);
                                        }
                                        lastClickKey = e;
                                        setTimeout(() => {
                                            lastClickKey = '';
                                        }, 300)
                                    }}
                                />
                            </div>
                        </Pane>
                    </SplitPane>
                </div>
            </ApiManageProjectInfoContext.Provider>
            <Modal
                title={editCatalogId ? '编辑目录' : '新建目录'}
                open={isCreateCatalogModalOpen}
                onCancel={handleCreateCatalogModalCancel}
                onOk={handleCreateCatalogModalOk}
                okButtonProps={{ loading: createCatalogLoading }}
                width="480px"
            >
                <CreateCatalog ref={createCatalogRef} />
            </Modal>
        </>
    );
}
