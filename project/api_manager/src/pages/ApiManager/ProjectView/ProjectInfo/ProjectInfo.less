@import '~@lynx/design-token/dist/less/token.less';

.projectInfo {
    height: 100%;
    display: flex;
    background: #fff;
}

.splitPane {
    :global {
        .cjjWdp {
            background-color: @color-border-table !important;
            opacity: 1 !important;
        }

        .fXAXjb {
            &:hover {
                cursor: default;
            }
        }

        .ciqkXk,
        .cqKmWU {
            background: #bbb !important;
            border-color: #fff;
        }
    }
}

.splitPaneResizer {
    .splitPane();

    :global {
        .cjjWdp {
            &:hover {
                background-color: @color-bg-brand !important;
                transition-duration: 0s;
            }
        }

        .ciqkXk,
        .cqKmWU {
            background: #bbb !important;
            border-color: #fff;
        }

        .fXAXjb {
            &:hover {
                cursor: col-resize;
            }
        }
    }
}

.hiddenLeftPaneContent {
    :global {
        .Resizer {
            cursor: default;
        }
    }
}

.rightPane {
    position: relative;
    height: 100%;
    // padding: 12px 12px 0;
}

.tabsContainer {
    height: 100%;

    :global {
        .ant-tabs-editable {
            >.ant-tabs-nav {
                padding: 12px 12px 0 !important;
                margin-bottom: 0;

                >.ant-tabs-nav-wrap {
                    >.ant-tabs-nav-list {
                        >.ant-tabs-tab {
                            background-color: #fff !important;
                            border: none !important;
                            max-width: 172px;
                            margin: 0 !important;
                            border-bottom: 1px solid #EBEDF0ff !important;
                            padding: 6px 7px !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: flex-start !important;

                            .ant-tabs-tab-btn {
                                flex: 1;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }

                            .ant-tabs-tab-remove {
                                flex: 0 0 24px;
                            }

                            // &::after {
                            //     content: '';
                            //     width: 1px;
                            //     height: 16px;
                            //     background: #EBEDF0ff;
                            // }
                        }

                        >.ant-tabs-tab-active {
                            border-radius: 4px 4px 0px 0px !important;
                            border-left: 1px solid #EBEDF0ff !important;
                            border-right: 1px solid #EBEDF0ff !important;
                            border-top: 1px solid #1133ADff !important;
                            border-bottom: none !important;
                        }

                        >.ant-tabs-tab-with-remove {
                            // .ant-tabs-tab-remove {
                            //     display: none;
                            // }
                        }

                        >.ant-tabs-nav-add {
                            border-bottom: 1px solid #EBEDF0ff !important;
                            border-left: none !important;
                            border-right: none !important;
                            border-top: none !important;
                            background: #fff !important;
                            color: #575859ff !important;

                            &::after {
                                content: '新建';
                                margin-left: 4px;
                                color: #575859ff !important;
                            }
                        }
                    }
                }
            }

            >.ant-tabs-content-holder {
                >.ant-tabs-content {
                    height: 100% !important;

                    >.ant-tabs-tabpane {
                        height: 100% !important;
                    }
                }
            }
        }
    }
}

.tabTitle {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;

    &:hover {
        .tabClose {
            display: block;
        }
    }


    .tabTitleText {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #252526;
    }

    &.italic {
        font-style: italic;
    }

    .tabClose {
        margin-left: 4px;
        display: none;
    }
}